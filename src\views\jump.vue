<template>
  <div class="project-select-container">
    <!-- 上半部分：欢迎横幅和用户信息 -->
    <div class="top-section">
      <div class="welcome-banner">
        <div class="banner-background">
          <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
          </div>
        </div>
        <div class="welcome-content">
          <div class="welcome-text">
            <h1 class="welcome-title">
              <span class="title-icon">
                <i class="el-icon-office-building"></i>
              </span>
              欢迎使用<span class="highlight">高效机房</span>
            </h1>
            <p class="welcome-subtitle">
              <i class="el-icon-star-on"></i>
              智能管理 · 高效协作 · 数据驱动
            </p>
          </div>
          <div class="welcome-stats">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-folder-opened"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ projectList.length }}</div>
                <div class="stat-text">可用项目</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <i class="el-icon-user"></i>
              </div>
              <div class="stat-info">
                <div class="stat-number">1</div>
                <div class="stat-text">在线用户</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="user-info-bar">
        <div class="user-profile">
          <div class="avatar-container">
            <el-avatar :size="60" :src="avatar" class="user-avatar">
              <i class="el-icon-user-solid"></i>
            </el-avatar>
            <div class="online-indicator"></div>
          </div>
          <div class="user-brief">
            <h3 class="user-name">{{ name || '用户' }}</h3>
            <div class="user-status">
              <span class="status-dot"></span>
              <span class="status-text">在线</span>
            </div>
          </div>
        </div>

        <div class="action-center">
          <div class="quick-actions">
            <el-button @click="refreshProjects" type="primary" size="small" class="action-btn">
              <i class="el-icon-refresh"></i>
              <span>刷新项目</span>
            </el-button>
            <el-button @click="goToHelp" type="default" size="small" class="action-btn">
              <i class="el-icon-question"></i>
              <span>使用帮助</span>
            </el-button>
          </div>
          <div class="time-info">
            <div class="current-time">{{ currentTime }}</div>
            <div class="current-date">{{ currentDate }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分：项目内容 -->
    <div class="bottom-section">
      <div class="projects-header">
        <div class="section-title">
          <i class="el-icon-collection"></i>
          <h2>选择项目</h2>
        </div>
      </div>

      <div class="projects-content" v-loading="loading">
        <div class="project-grid">


          <div
            v-for="project in projectList"
            :key="project.id"
            class="project-card"
            @click="selectProject(project)"
          >
            <div class="project-image">
              <div class="project-image-placeholder">
                <i class="el-icon-office-building" style="font-size: 40px;"></i>
              </div>
            </div>
            <div class="project-details">
              <h3 class="project-name">{{ project.name }}</h3>
              <div class="project-dept" v-if="project.deptName">
                <i class="el-icon-office-building"></i>
                <span>{{ project.deptName }}</span>
              </div>
              <div class="project-address" v-if="project.address">
                <i class="el-icon-location-outline"></i>
                <span>{{ project.address }}</span>
              </div>
              <div class="project-description" v-if="project.detail">
                {{ project.detail }}
              </div>
              <div class="project-meta">
                <div class="project-tags">
                  <el-tag v-if="project.status" :type="getStatusType(project.status)" size="mini">
                    {{ getStatusText(project.status) }}
                  </el-tag>
                </div>
              </div>
              <div class="project-action">
                <el-button type="primary" size="small">
                  <i class="el-icon-right"></i>进入项目
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 完全空状态 -->
        <div v-if="!loading && projectList.length === 0" class="empty-placeholder">
          <el-empty description="暂无可用项目">
            <p>请联系管理员为您分配项目权限</p>
          </el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listAll } from '@/api/biz/project'
import { mapGetters } from 'vuex'

export default {
  name: 'ProjectSelection',
  data() {
    return {
      loading: true,
      projectList: [],
      userInfo: {},
      currentTime: '',
      currentDate: '',
      timeTimer: null
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'avatar'
    ])
  },
  created() {
    this.getUserInfo()
    this.getProjectList()
    this.updateTime()
  },

  beforeDestroy() {
    if (this.timeTimer) {
      clearInterval(this.timeTimer)
    }
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      // 从 Vuex 获取用户信息
      this.userInfo = this.$store.getters || {}
    },

    // 获取项目列表
    async getProjectList() {
      try {
        this.loading = true
        const response = await listAll()
        this.projectList = response.data || response.rows || []
      } catch (error) {
        console.error('获取项目列表失败:', error)
        this.$message.error('获取项目列表失败，请稍后重试')
        this.projectList = []
      } finally {
        this.loading = false
      }
    },

    // 选择项目
    selectProject(project) {
      // 保存项目ID到本地存储，键名为 projectId
      localStorage.setItem('projectId', project.id)
      // 同时保存完整的项目信息供其他地方使用
      localStorage.setItem('selectedProject', JSON.stringify(project))

      // 跳转到主页面
      this.$router.push('/')
    },

    // 获取项目状态类型
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'planning': 'warning',
        'completed': 'info',
        'suspended': 'danger'
      }
      return statusMap[status] || 'info'
    },

    // 获取项目状态文本
    getStatusText(status) {
      const statusMap = {
        'active': '进行中',
        'planning': '规划中',
        'completed': '已完成',
        'suspended': '已暂停'
      }
      return statusMap[status] || '未知'
    },

    // 刷新项目列表
    refreshProjects() {
      this.getProjectList()
      this.$message.success('项目列表已刷新')
    },

    // 使用帮助
    goToHelp() {
      this.$message.info('即将跳转到使用帮助页面')
    },

    // 更新时间
    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      })
      this.currentDate = now.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.project-select-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 50%, #e8ebff 100%);
  display: flex;
  flex-direction: column;
}

/* 上半部分样式 */
.top-section {
  flex: 0 0 auto;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #1a2980 100%);
  padding: 50px 0;
  color: white;
  position: relative;
  overflow: hidden;
}

/* 浮动装饰元素 */
.banner-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  top: 30%;
  right: 30%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.welcome-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

.welcome-text {
  flex: 1;
  text-align: left;

  .welcome-title {
    font-size: 2.8rem;
    font-weight: 300;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    line-height: 1.2;

    .title-icon {
      margin-right: 15px;
      font-size: 2.5rem;
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: pulse 2s ease-in-out infinite;
    }

    .highlight {
      font-weight: 600;
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-left: 8px;
    }
  }

  .welcome-subtitle {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.9;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
      color: #ffd700;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.welcome-stats {
  display: flex;
  gap: 20px;

  .stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 140px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover {
      transform: translateY(-5px);
      background: rgba(255, 255, 255, 0.2);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .stat-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #ffd700, #ffed4e);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 24px;
        color: #1a2980;
      }
    }

    .stat-info {
      .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 4px;
        color: white;
      }

      .stat-text {
        font-size: 0.9rem;
        opacity: 0.9;
        color: white;
      }
    }
  }
}

.user-info-bar {
  background: white;
  padding: 25px 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-top: 3px solid transparent;
  background-image: linear-gradient(white, white), linear-gradient(90deg, #1a2980, #26d0ce);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(26, 41, 128, 0.3), transparent);
  }
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 20px;

  .avatar-container {
    position: relative;

    .user-avatar {
      border: 3px solid transparent;
      background: linear-gradient(white, white) padding-box,
                  linear-gradient(135deg, #1a2980, #26d0ce) border-box;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(26, 41, 128, 0.3);
      }
    }

    .online-indicator {
      position: absolute;
      bottom: 2px;
      right: 2px;
      width: 16px;
      height: 16px;
      background: #52c41a;
      border: 3px solid white;
      border-radius: 50%;
      animation: pulse-online 2s ease-in-out infinite;
    }
  }

  .user-brief {
    .user-name {
      margin: 0 0 6px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: #303133;
      background: linear-gradient(135deg, #1a2980, #26d0ce);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .user-dept {
      margin: 0 0 8px 0;
      font-size: 0.9rem;
      color: #606266;
      display: flex;
      align-items: center;

      i {
        margin-right: 6px;
        color: #1a2980;
      }
    }

    .user-status {
      display: flex;
      align-items: center;
      gap: 6px;

      .status-dot {
        width: 8px;
        height: 8px;
        background: #52c41a;
        border-radius: 50%;
        animation: pulse-online 2s ease-in-out infinite;
      }

      .status-text {
        font-size: 0.8rem;
        color: #52c41a;
        font-weight: 500;
      }
    }
  }
}

@keyframes pulse-online {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.action-center {
  display: flex;
  align-items: center;
  gap: 30px;

  .quick-actions {
    display: flex;
    gap: 12px;

    .action-btn {
      border-radius: 20px;
      padding: 8px 20px;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      border: 1px solid #e4e7ed;

      &.el-button--primary {
        background: linear-gradient(135deg, #1a2980, #26d0ce);
        border: none;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(26, 41, 128, 0.3);
          background: linear-gradient(135deg, #26d0ce, #1a2980);
        }
      }

      &.el-button--default {
        background: white;
        color: #606266;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          border-color: #1a2980;
          color: #1a2980;
        }
      }

      span {
        margin-left: 6px;
      }
    }
  }

  .time-info {
    text-align: right;

    .current-time {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1a2980;
      margin-bottom: 2px;
      font-family: 'Courier New', monospace;
    }

    .current-date {
      font-size: 0.85rem;
      color: #909399;
      font-weight: 500;
    }
  }
}

/* 下半部分样式 */
.bottom-section {
  flex: 1;
  padding: 20px 60px 40px;
  max-width: 1400px;
  margin: 0 auto;
}

.projects-header {
  margin-bottom: 25px;
}

.section-title {
  display: flex;
  align-items: center;

  i {
    font-size: 20px;
    margin-right: 8px;
    color: #667eea;
  }

  h2 {
    margin: 0;
    font-size: 18px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
  }
}



/* 项目网格样式 */
.projects-content {
  min-height: 400px;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 16px;
}

.project-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  height: 260px;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(102, 126, 234, 0.2);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #1a2980);
    border-radius: 16px 16px 0 0;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 16px;
  }

  &:hover {
    transform: translateY(-12px);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.25);
    border-color: rgba(102, 126, 234, 0.4);

    &::after {
      opacity: 1;
    }

    .project-image {
      background: linear-gradient(135deg, #667eea, #764ba2);

      .project-image-placeholder {
        color: white;
        opacity: 1;
        transform: scale(1.1);
      }
    }

    .project-name {
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
}

.project-image {
  height: 80px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
    transition: left 0.6s ease;
  }
}

.project-card:hover .project-image::before {
  left: 100%;
}

.project-image-placeholder {
  color: #667eea;
  opacity: 0.8;
  transition: all 0.4s ease;
}

.project-details {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-name {
  font-size: 15px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #303133;
  line-height: 1.4;
  height: 2.8em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.project-dept,
.project-address {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: rgba(102, 126, 234, 0.8);
  margin-bottom: 6px;

  i {
    margin-right: 4px;
    font-size: 12px;
    color: #667eea;
    opacity: 0.8;
  }

  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.project-description {
  font-size: 12px;
  color: rgba(96, 98, 102, 0.9);
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  flex: 1;
  min-height: 2.8em;
}

.project-meta {
  margin-bottom: 12px;

  .project-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;

    .el-tag {
      height: 22px;
      line-height: 20px;
      font-size: 10px;
      border-radius: 11px;
      padding: 0 10px;
      border: none;
      font-weight: 500;

      &.el-tag--success {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
        color: #667eea;
        border: 1px solid rgba(102, 126, 234, 0.3);
      }

      &.el-tag--warning {
        background: linear-gradient(135deg, rgba(255, 193, 7, 0.15), rgba(255, 152, 0, 0.15));
        color: #ff9800;
        border: 1px solid rgba(255, 152, 0, 0.3);
      }

      &.el-tag--info {
        background: linear-gradient(135deg, rgba(144, 147, 153, 0.15), rgba(96, 98, 102, 0.15));
        color: #909399;
        border: 1px solid rgba(144, 147, 153, 0.3);
      }

      &.el-tag--danger {
        background: linear-gradient(135deg, rgba(245, 108, 108, 0.15), rgba(255, 77, 79, 0.15));
        color: #f56c6c;
        border: 1px solid rgba(245, 108, 108, 0.3);
      }
    }
  }
}

.project-action {
  display: flex;
  justify-content: center;
  margin-top: auto;

  .el-button {
    background: linear-gradient(135deg, #1a2980, #26d0ce);
    border: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 12px;
    height: 32px;
    padding: 0 20px;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(26, 41, 128, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s ease;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(26, 41, 128, 0.4);
      background: linear-gradient(135deg, #26d0ce, #1a2980);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(-1px);
    }
  }
}
/* 空状态样式 */
.empty-placeholder {
  text-align: center;
  padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .project-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1200px) {
  .project-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .bottom-section {
    padding: 20px 40px 40px;
  }
}

@media (max-width: 992px) {
  .project-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .welcome-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;

    .welcome-text .welcome-title {
      justify-content: center;
    }

    .welcome-stats {
      justify-content: center;
    }
  }

  .user-info-bar {
    flex-direction: column;
    gap: 25px;
    text-align: center;
    padding: 20px 40px;

    .action-center {
      flex-direction: column;
      gap: 20px;
    }
  }
}

@media (max-width: 768px) {
  .bottom-section {
    padding: 15px 20px 30px;
  }

  .project-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 12px;
  }

  .project-card {
    height: 240px;
  }



  .welcome-banner {
    padding: 40px 20px;
  }

  .welcome-content {
    padding: 0 20px;
  }

  .welcome-text .welcome-title {
    font-size: 2.2rem;
    flex-direction: column;
    gap: 10px;
  }

  .welcome-stats {
    flex-direction: column;
    gap: 15px;

    .stat-card {
      min-width: auto;
      width: 100%;
      max-width: 300px;
    }
  }

  .user-info-bar {
    padding: 20px;

    .user-profile {
      flex-direction: column;
      gap: 15px;
    }

    .action-center .quick-actions {
      flex-direction: column;
      width: 100%;

      .action-btn {
        width: 100%;
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .welcome-text .welcome-title {
    font-size: 1.8rem;

    .title-icon {
      font-size: 2rem;
    }
  }

  .welcome-banner {
    padding: 30px 15px;
  }

  .welcome-content {
    padding: 0 15px;
  }

  .floating-shapes .shape {
    display: none; // 在小屏幕上隐藏装饰元素
  }

  .project-grid {
    grid-template-columns: 1fr;
  }

  .project-card {
    height: 220px;
  }

  .bottom-section {
    padding: 15px 15px 25px;
  }

  .user-info-bar {
    padding: 15px;
  }
}

</style>
