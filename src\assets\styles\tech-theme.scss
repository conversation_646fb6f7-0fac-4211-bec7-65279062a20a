/**
 * 深蓝科技主题样式
 * 与登录页面保持一致的科技感设计
 */

// 全局背景和基础样式
body {
  background: linear-gradient(135deg, #0a1428 0%, #1a2744 50%, #0f1b2e 100%);
  background-attachment: fixed;
}

#app {
  background: transparent;
}

// 主容器背景
.main-container {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
}

// 内容区域样式
.app-container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  border: 1px solid rgba(0, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

// 卡片样式增强
.el-card {
  background: rgba(255, 255, 255, 0.08) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  
  .el-card__header {
    background: rgba(0, 255, 255, 0.05) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: #ffffff !important;
  }
  
  .el-card__body {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}

// 表格样式增强
.el-table {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  
  .el-table__header-wrapper {
    background: rgba(0, 255, 255, 0.1) !important;
    
    th {
      background: transparent !important;
      color: #00ffff !important;
      border-bottom: 1px solid rgba(0, 255, 255, 0.3) !important;
    }
  }
  
  .el-table__body-wrapper {
    tr {
      background: rgba(255, 255, 255, 0.02) !important;
      
      &:hover {
        background: rgba(0, 255, 255, 0.05) !important;
      }
      
      td {
        border-bottom: 1px solid rgba(0, 255, 255, 0.1) !important;
        color: rgba(255, 255, 255, 0.9) !important;
      }
    }
  }
}

// 表单样式增强
.el-form {
  .el-form-item__label {
    color: rgba(255, 255, 255, 0.8) !important;
  }
  
  .el-input__inner {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
    color: #ffffff !important;
    
    &:focus {
      border-color: #00ccff !important;
      box-shadow: 0 0 0 2px rgba(0, 204, 255, 0.2) !important;
    }
    
    &::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
  
  .el-textarea__inner {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
    color: #ffffff !important;
    
    &:focus {
      border-color: #00ccff !important;
      box-shadow: 0 0 0 2px rgba(0, 204, 255, 0.2) !important;
    }
    
    &::placeholder {
      color: rgba(255, 255, 255, 0.5) !important;
    }
  }
}

// 选择器样式
.el-select {
  .el-input__inner {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(0, 255, 255, 0.3) !important;
    color: #ffffff !important;
  }
}

// 下拉菜单样式
.el-dropdown-menu {
  background: rgba(26, 39, 68, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  
  .el-dropdown-menu__item {
    color: rgba(255, 255, 255, 0.8) !important;
    
    &:hover {
      background: rgba(0, 255, 255, 0.1) !important;
      color: #00ffff !important;
    }
  }
}

// 分页器样式
.el-pagination {
  .el-pager li {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    
    &:hover {
      background: rgba(0, 255, 255, 0.1) !important;
      color: #00ffff !important;
    }
    
    &.active {
      background: #00ccff !important;
      color: #ffffff !important;
    }
  }
  
  .btn-prev, .btn-next {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    
    &:hover {
      background: rgba(0, 255, 255, 0.1) !important;
      color: #00ffff !important;
    }
  }
  
  .el-pagination__total,
  .el-pagination__jump {
    color: rgba(255, 255, 255, 0.8) !important;
  }
}

// 对话框样式
.el-dialog {
  background: rgba(26, 39, 68, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  
  .el-dialog__header {
    background: rgba(0, 255, 255, 0.1) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2) !important;
    
    .el-dialog__title {
      color: #00ffff !important;
    }
    
    .el-dialog__close {
      color: rgba(255, 255, 255, 0.8) !important;
      
      &:hover {
        color: #00ffff !important;
      }
    }
  }
  
  .el-dialog__body {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}

// 消息提示样式
.el-message {
  background: rgba(26, 39, 68, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

// 通知样式
.el-notification {
  background: rgba(26, 39, 68, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  
  .el-notification__title {
    color: #00ffff !important;
  }
  
  .el-notification__content {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}

// 标签页样式
.el-tabs {
  .el-tabs__header {
    background: rgba(0, 255, 255, 0.05) !important;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2) !important;
    
    .el-tabs__item {
      color: rgba(255, 255, 255, 0.7) !important;
      
      &:hover {
        color: #00ffff !important;
      }
      
      &.is-active {
        color: #00ffff !important;
        border-bottom-color: #00ccff !important;
      }
    }
  }
  
  .el-tabs__content {
    color: rgba(255, 255, 255, 0.9) !important;
  }
}

// 面包屑样式
.el-breadcrumb {
  .el-breadcrumb__item {
    .el-breadcrumb__inner {
      color: rgba(255, 255, 255, 0.7) !important;
      
      &:hover {
        color: #00ffff !important;
      }
    }
    
    &:last-child .el-breadcrumb__inner {
      color: #00ffff !important;
    }
  }
  
  .el-breadcrumb__separator {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  
  &:hover {
    background: rgba(0, 255, 255, 0.5);
  }
}
