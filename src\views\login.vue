<template>
  <div class="login">
    <!-- 左侧装饰区域 -->
    <div class="login-left">
      <div class="tech-decoration">
        <div class="floating-particles"></div>
        <div class="grid-overlay"></div>
        <div class="brand-content">
          <h1 class="brand-title">高效机房管理系统</h1>
          <p class="brand-subtitle">智能化数据中心管理平台</p>
          <div class="feature-list">
            <div class="feature-item">
              <i class="feature-icon">🚀</i>
              <span>智能监控</span>
            </div>
            <div class="feature-item">
              <i class="feature-icon">⚡</i>
              <span>高效管理</span>
            </div>
            <div class="feature-item">
              <i class="feature-icon">🔒</i>
              <span>安全可靠</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单 -->
    <div class="login-right">
      <div class="login-container">
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <div class="form-header">
            <h3 class="title">欢迎登录</h3>
            <p class="subtitle">请输入您的账号信息</p>
          </div>

          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              auto-complete="off"
              placeholder="请输入账号"
              class="custom-input"
            >
              <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              auto-complete="off"
              placeholder="请输入密码"
              class="custom-input"
              @keyup.enter.native="handleLogin"
            >
              <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
            </el-input>
          </el-form-item>

          <el-form-item prop="code" v-if="captchaEnabled">
            <el-input
              v-model="loginForm.code"
              auto-complete="off"
              placeholder="请输入验证码"
              style="width: 63%"
              class="custom-input"
              @keyup.enter.native="handleLogin"
            >
              <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img"/>
            </div>
          </el-form-item>

          <div class="form-options">
            <el-checkbox v-model="loginForm.rememberMe" class="remember-checkbox">
              记住密码
            </el-checkbox>
            <router-link class="forgot-link" to="/forgot" v-if="false">忘记密码？</router-link>
          </div>

          <el-form-item style="width:100%; margin-bottom: 10px;">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              class="login-btn"
              @click.native.prevent="handleLogin"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>

          <div class="register-link" v-if="register">
            <span>还没有账号？</span>
            <router-link class="link-type" :to="'/register'">立即注册</router-link>
          </div>
        </el-form>
      </div>
    </div>

    <!--  底部版权信息  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2025 JIANYOU All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      loginForm: {
        username: "admin",
        password: "admin123",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.data.captchaEnabled === undefined ? true : res.data.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.data.img;
          this.loginForm.uuid = res.data.uuid;
        }
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            // 登录成功后跳转到项目选择页面
            this.$router.push({ path: this.redirect || "/jump" }).catch(()=>{});
          }).catch(() => {
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #0a1428 0%, #1a2744 50%, #0f1b2e 100%);
  position: relative;
  overflow: hidden;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 30%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(0, 150, 255, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(100, 200, 255, 0.06) 0%, transparent 50%);
    animation: backgroundShift 20s ease-in-out infinite;
  }
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

// 左侧装饰区域
.login-left {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .tech-decoration {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .floating-particles {
      position: absolute;
      width: 100%;
      height: 100%;
      background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(0, 255, 255, 0.8), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(100, 200, 255, 0.6), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 150, 255, 0.5), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(0, 255, 255, 0.4), transparent);
      background-repeat: repeat;
      background-size: 200px 100px;
      animation: particleFloat 15s linear infinite;
    }

    .grid-overlay {
      position: absolute;
      width: 100%;
      height: 100%;
      background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
      background-size: 50px 50px;
      opacity: 0.3;
    }

    .brand-content {
      position: relative;
      z-index: 2;
      text-align: center;
      color: white;

      .brand-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #00ffff, #0099ff, #66ccff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        animation: titleGlow 3s ease-in-out infinite alternate;
        line-height: 1.2;
        letter-spacing: 2px;
      }

      .brand-subtitle {
        font-size: 1.2rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 3rem;
        letter-spacing: 2px;
      }

      .feature-list {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;

        .feature-item {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
          padding: 1rem;
          background: rgba(0, 255, 255, 0.1);
          border: 1px solid rgba(0, 255, 255, 0.3);
          border-radius: 10px;
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(0, 255, 255, 0.2);
            transform: translateX(10px);
            box-shadow: 0 5px 20px rgba(0, 255, 255, 0.3);
          }

          .feature-icon {
            font-size: 1.5rem;
          }

          span {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
          }
        }
      }
    }
  }
}

@keyframes particleFloat {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

@keyframes titleGlow {
  0% { text-shadow: 0 0 30px rgba(0, 255, 255, 0.5); }
  100% { text-shadow: 0 0 50px rgba(0, 255, 255, 0.8), 0 0 70px rgba(0, 150, 255, 0.6); }
}

// 右侧登录区域
.login-right {
  width: 480px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border-left: 1px solid rgba(0, 255, 255, 0.2);
  }

  .login-container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 400px;
  }
}

.login-form {
  background: rgba(26, 39, 68, 0.85);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  position: relative;

  .form-header {
    text-align: center;
    margin-bottom: 2rem;

    .title {
      font-size: 1.8rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
      background: linear-gradient(135deg, #00ffff, #66ccff, #ffffff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.9rem;
      margin: 0;
      opacity: 0.8;
    }
  }

  .el-form-item {
    margin-bottom: 1.5rem;

    .custom-input {
      .el-input__inner {
        height: 50px;
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.1);
        color: #ffffff;
        font-size: 1rem;
        padding-left: 45px;
        transition: all 0.3s ease;

        &:focus {
          border-color: #00ccff;
          background: rgba(255, 255, 255, 0.15);
          box-shadow: 0 0 0 3px rgba(0, 204, 255, 0.2);
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
          font-size: 0.9rem;
        }
      }

      .el-input__prefix {
        left: 15px;
        top: 0;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;

        .input-icon {
          color: #00ccff;
          font-size: 1.1rem;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    .remember-checkbox {
      .el-checkbox__label {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
      }

      .el-checkbox__input .el-checkbox__inner {
        border-color: rgba(0, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.1);
      }

      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #00ccff;
        border-color: #00ccff;
      }
    }

    .forgot-link {
      color: #00ccff;
      text-decoration: none;
      font-size: 0.9rem;
      transition: color 0.3s ease;

      &:hover {
        color: #66ccff;
      }
    }
  }

  .login-btn {
    width: 100%;
    height: 50px;
    background: linear-gradient(135deg, #00ccff, #0099ff);
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 204, 255, 0.3);

    &:hover {
      background: linear-gradient(135deg, #0099ff, #0077cc);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 204, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    &.is-loading {
      background: linear-gradient(135deg, #00ccff, #0099ff);
    }
  }

  .register-link {
    text-align: center;
    margin-top: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;

    .link-type {
      color: #00ccff;
      text-decoration: none;
      font-weight: 500;
      margin-left: 0.5rem;
      transition: color 0.3s ease;

      &:hover {
        color: #66ccff;
      }
    }
  }
}

.login-code {
  width: 33%;
  height: 50px;
  float: right;

  .login-code-img {
    height: 50px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid rgba(0, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:hover {
      border-color: #00ccff;
      box-shadow: 0 2px 8px rgba(0, 204, 255, 0.2);
    }
  }
}

.el-login-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  letter-spacing: 1px;
  border-top: 1px solid rgba(0, 255, 255, 0.1);
}

// 全局修复 Element UI 输入框图标居中问题
.el-input__prefix {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.el-input__icon {
  line-height: 1 !important;
}
</style>
